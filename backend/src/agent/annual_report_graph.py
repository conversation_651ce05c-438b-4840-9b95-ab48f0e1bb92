import os
import re
from typing import List, Dict, Any

from agent.tools_and_schemas import SearchQueryList, Reflection
from dotenv import load_dotenv
from langchain_core.messages import AIMessage
from langgraph.types import Send
from langgraph.graph import StateGraph
from langgraph.graph import START, END
from langchain_core.runnables import RunnableConfig
from google.genai import Client

from agent.state import (
    OverallState,
    QueryGenerationState,
    ReflectionState,
    WebSearchState,
)
from agent.configuration import Configuration
from agent.prompts import (
    get_current_date,
)
from langchain_google_genai import ChatGoogleGenerativeAI
from agent.utils import (
    get_citations,
    get_research_topic,
    insert_citation_markers,
    resolve_urls,
)

load_dotenv()

if os.getenv("GEMINI_API_KEY") is None:
    raise ValueError("GEMINI_API_KEY is not set")

# Used for Google Search API
genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))

# Custom prompts for annual report search
power_plant_query_instructions = """Your goal is to generate a search query to find the holding company of a power plant.

Instructions:
- The user has provided a power plant name.
- Create a search query to find the holding company that owns this power plant.
- Format the query as "{power_plant_name} power plant holding company owner"
- The current date is {current_date}.

Format: 
- Format your response as a JSON object with ALL two of these exact keys:
   - "rationale": Brief explanation of why this query is relevant
   - "query": A list containing a single search query

Context: {research_topic}
"""

holding_company_annual_report_instructions = """Your goal is to generate a search query to find annual reports for a holding company.

Instructions:
- The user is looking for annual reports of a holding company.
- Create a search query to find the official annual reports for this company.
- Format the query as "{holding_company_name} annual reports investor relations financial statements"
- The current date is {current_date}.

Format: 
- Format your response as a JSON object with ALL two of these exact keys:
   - "rationale": Brief explanation of why this query is relevant
   - "query": A list containing a single search query

Context: {research_topic}
"""

web_searcher_instructions = """Conduct targeted Google Searches to gather information on "{research_topic}" and extract relevant information.

Instructions:
- The current date is {current_date}.
- Conduct a thorough search to gather comprehensive information.
- If this is a search for a power plant's holding company, identify and clearly state the name of the holding company that owns the power plant.
- If this is a search for annual reports, focus on finding links to official annual report PDFs from the company's website, especially from investor relations or financial sections.
- Only include information found in the search results, don't make up any information.

Research Topic:
{research_topic}
"""

annual_report_extraction_instructions = """You are an expert at finding annual report PDFs on company websites.

Instructions:
- Analyze the search results to identify the official website of {holding_company}.
- Look specifically for sections like "Investor Relations", "Financial Information", "Financial Statements", or "Annual Reports".
- Extract ONLY direct PDF links to annual reports for the past 5 years if available.
- If annual reports and financial statements are separately mentioned, focus only on annual reports.
- Only include PDF links, not website page links.
- Look for patterns like "Annual Report 2023.pdf", "2023 Annual Report.pdf", etc.
- If you find links to annual report landing pages but not direct PDF links, explain that you need to visit those pages to find the PDFs.
- If no PDF links are found, explain why and suggest alternative approaches.

Summaries:
{summaries}
"""

final_answer_instructions = """Generate a clear response with the annual report PDF links for {holding_company}.

Instructions:
- The current date is {current_date}.
- Start with a brief introduction stating that you've found the annual reports for the holding company of the power plant.
- Present the PDF links to annual reports in a clear, organized format, preferably in a markdown list with years.
- Format should be: "- [Annual Report YYYY](PDF_URL)"
- Include only PDF links, not website links.
- If possible, provide annual reports for the past 5 years.
- If no PDF links were found, explain why and suggest alternative approaches.
- Include the sources you used in the answer correctly, use markdown format (e.g. [apnews](https://vertexaisearch.cloud.google.com/id/1-0)). THIS IS A MUST.

Research Topic:
{research_topic}

Summaries:
{summaries}
"""

# Nodes
def extract_power_plant_name(state: OverallState) -> str:
    """Extract the power plant name from the user's question."""
    research_topic = get_research_topic(state["messages"])
    # The research topic is expected to be just the power plant name
    print(f"Extracted power plant name: {research_topic.strip()}")
    return research_topic.strip()

def generate_holding_company_query(state: OverallState, config: RunnableConfig) -> QueryGenerationState:
    """LangGraph node that generates a search query to find the holding company of a power plant.
    
    Args:
        state: Current graph state containing the User's question (power plant name)
        config: Configuration for the runnable, including LLM provider settings
    
    Returns:
        Dictionary with state update, including search_query key containing the generated query
    """
    print("Generating holding company query, state:", state)
    configurable = Configuration.from_runnable_config(config)
    
    # Initialize state variables if not present
    if state.get("initial_search_query_count") is None:
        state["initial_search_query_count"] = 1  # We only need one query for this step
    
    # Initialize the power plant name if not present
    if state.get("power_plant_name") is None:
        state["power_plant_name"] = extract_power_plant_name(state)
        print(f"Set power_plant_name to: {state['power_plant_name']}")
    
    # init Gemini model
    llm = ChatGoogleGenerativeAI(
        model=configurable.query_generator_model,
        temperature=0.2,  # Lower temperature for more focused results
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    structured_llm = llm.with_structured_output(SearchQueryList)
    
    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = power_plant_query_instructions.format(
        current_date=current_date,
        research_topic=state["power_plant_name"],
    )
    
    # Generate the search query
    result = structured_llm.invoke(formatted_prompt)
    return {"search_query": result.query}

def continue_to_holding_company_search(state: QueryGenerationState):
    """LangGraph node that sends the search query to the web research node."""
    return [
        Send("holding_company_search", {"search_query": search_query, "id": int(idx)})
        for idx, search_query in enumerate(state["search_query"])
    ]

def holding_company_search(state: WebSearchState, config: RunnableConfig) -> OverallState:
    """LangGraph node that performs web research to find the holding company.
    
    Args:
        state: Current graph state containing the search query
        config: Configuration for the runnable, including search API settings
    
    Returns:
        Dictionary with state update, including sources_gathered and web_research_result
    """
    # Configure
    configurable = Configuration.from_runnable_config(config)
    formatted_prompt = web_searcher_instructions.format(
        current_date=get_current_date(),
        research_topic=state["search_query"],
    )
    
    # Use Google Search API
    response = genai_client.models.generate_content(
        model=configurable.query_generator_model,
        contents=formatted_prompt,
        config={
            "tools": [{"google_search": {}}],
            "temperature": 0,
        },
    )
    
    # Process the response
    resolved_urls = resolve_urls(
        response.candidates[0].grounding_metadata.grounding_chunks, state["id"]
    )
    citations = get_citations(response, resolved_urls)
    modified_text = insert_citation_markers(response.text, citations)
    sources_gathered = [item for citation in citations for item in citation["segments"]]
    
    # Extract the holding company name from the response
    holding_company = extract_holding_company_from_text(response.text)
    
    return {
        "sources_gathered": sources_gathered,
        "search_query": [state["search_query"]],
        "web_research_result": [modified_text],
        "holding_company": holding_company,
    }

def extract_holding_company_from_text(text: str) -> str:
    """Extract the holding company name from the search result text."""
    # Look for explicit statements about ownership
    patterns = [
        r"(?:owned|operated|managed|controlled|run)\s+by\s+([A-Z][A-Za-z0-9\s\.,&'-]+?)(?:\.|\,|\(|\)|\n|$)",
        r"(?:subsidiary|part|division|asset)\s+of\s+([A-Z][A-Za-z0-9\s\.,&'-]+?)(?:\.|\,|\(|\)|\n|$)",
        r"([A-Z][A-Za-z0-9\s\.,&'-]+?)(?:\s+is|\s+owns|\s+operates|\s+controls)\s+the\s+power\s+plant",
        r"holding\s+company\s+(?:is|of|for)\s+([A-Z][A-Za-z0-9\s\.,&'-]+?)(?:\.|\,|\(|\)|\n|$)",
        r"parent\s+company\s+(?:is|of)\s+([A-Z][A-Za-z0-9\s\.,&'-]+?)(?:\.|\,|\(|\)|\n|$)",
        r"(?:owned|operated|managed)\s+(?:and|&)?\s*(?:maintained|controlled)?\s+by\s+([A-Z][A-Za-z0-9\s\.,&'-]+?)(?:\.|\,|\(|\)|\n|$)",
        r"(?:a|an)\s+(?:subsidiary|affiliate|unit)\s+of\s+([A-Z][A-Za-z0-9\s\.,&'-]+?)(?:\.|\,|\(|\)|\n|$)",
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, text)
        if matches:
            # Return the first match, cleaned up
            company_name = matches[0].strip()
            # Remove trailing words like "and", "which", etc.
            company_name = re.sub(r'\s+(?:and|which|that|a|an|the)\s+.*$', '', company_name)
            return company_name
    
    # Look for sentences that mention the power plant and a company in close proximity
    sentences = re.split(r'[.!?]', text)
    for sentence in sentences:
        if "power plant" in sentence.lower() or "power station" in sentence.lower() or "generating station" in sentence.lower():
            # Look for company names with common suffixes in this sentence
            company_suffixes = ["Inc", "LLC", "Ltd", "Corporation", "Corp", "Company", "Co", "Group", "Holdings", "Energy", "Power"]
            for suffix in company_suffixes:
                suffix_pattern = r'([A-Z][A-Za-z0-9\s\.,&\'-]+?\s+' + suffix + r')(?:\.|\,|\s|\(|\)|\n|$)'
                company_matches = re.findall(suffix_pattern, sentence)
                if company_matches:
                    return company_matches[0].strip()
    
    # If no clear pattern match, look for company names with common suffixes in the entire text
    company_suffixes = ["Inc", "LLC", "Ltd", "Corporation", "Corp", "Company", "Co", "Group", "Holdings", "Energy", "Power"]
    for suffix in company_suffixes:
        suffix_pattern = r'([A-Z][A-Za-z0-9\s\.,&\'-]+?\s+' + suffix + r')(?:\.|\,|\s|\(|\)|\n|$)'
        company_matches = re.findall(suffix_pattern, text)
        if company_matches:
            return company_matches[0].strip()
    
    # If all else fails, return a generic placeholder
    return "the holding company"

def generate_annual_report_query(state: OverallState, config: RunnableConfig) -> QueryGenerationState:
    """LangGraph node that generates a search query to find annual reports for the holding company.
    
    Args:
        state: Current graph state containing the holding company name
        config: Configuration for the runnable, including LLM provider settings
    
    Returns:
        Dictionary with state update, including search_query key containing the generated query
    """
    configurable = Configuration.from_runnable_config(config)
    
    # init Gemini model
    llm = ChatGoogleGenerativeAI(
        model=configurable.query_generator_model,
        temperature=0.2,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    structured_llm = llm.with_structured_output(SearchQueryList)
    
    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = holding_company_annual_report_instructions.format(
        current_date=current_date,
        research_topic=state["holding_company"],
    )
    
    # Generate the search query
    result = structured_llm.invoke(formatted_prompt)
    return {"search_query": result.query}

def continue_to_annual_report_search(state: QueryGenerationState):
    """LangGraph node that sends the search query to the annual report search node."""
    return [
        Send("annual_report_search", {"search_query": search_query, "id": int(idx)})
        for idx, search_query in enumerate(state["search_query"])
    ]

def annual_report_search(state: WebSearchState, config: RunnableConfig) -> OverallState:
    """LangGraph node that performs web research to find annual reports.
    
    Args:
        state: Current graph state containing the search query
        config: Configuration for the runnable, including search API settings
    
    Returns:
        Dictionary with state update, including sources_gathered and web_research_result
    """
    # Configure
    configurable = Configuration.from_runnable_config(config)
    formatted_prompt = web_searcher_instructions.format(
        current_date=get_current_date(),
        research_topic=state["search_query"],
    )
    
    # Use Google Search API
    response = genai_client.models.generate_content(
        model=configurable.query_generator_model,
        contents=formatted_prompt,
        config={
            "tools": [{"google_search": {}}],
            "temperature": 0,
        },
    )
    
    # Process the response
    resolved_urls = resolve_urls(
        response.candidates[0].grounding_metadata.grounding_chunks, state["id"]
    )
    citations = get_citations(response, resolved_urls)
    modified_text = insert_citation_markers(response.text, citations)
    sources_gathered = [item for citation in citations for item in citation["segments"]]
    
    return {
        "sources_gathered": sources_gathered,
        "search_query": [state["search_query"]],
        "web_research_result": [modified_text],
    }

def extract_annual_reports(state: OverallState, config: RunnableConfig) -> OverallState:
    """LangGraph node that extracts annual report PDF links from the search results.
    
    Args:
        state: Current graph state containing the web research results
        config: Configuration for the runnable, including LLM provider settings
    
    Returns:
        Dictionary with state update, including annual_report_links
    """
    configurable = Configuration.from_runnable_config(config)
    
    # init Gemini model
    llm = ChatGoogleGenerativeAI(
        model=configurable.reflection_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    # Format the prompt
    formatted_prompt = annual_report_extraction_instructions.format(
        holding_company=state["holding_company"],
        summaries="\n---\n\n".join(state["web_research_result"]),
    )
    
    # Extract annual report links
    result = llm.invoke(formatted_prompt)
    
    # Extract PDF links from the result
    pdf_links = extract_pdf_links(result.content)
    
    return {
        "annual_report_links": pdf_links,
        "extraction_result": result.content,
    }

def extract_pdf_links(text: str) -> List[str]:
    """Extract PDF links from text."""
    # Pattern to match URLs ending with .pdf
    # This handles various URL formats including those with query parameters
    pdf_pattern = r'https?://[^\s()<>\"\']+?\.pdf(?:[^\s()<>\"\']*?)?'
    pdf_links = re.findall(pdf_pattern, text)
    
    # Clean up the links (remove trailing punctuation, quotes, etc.)
    cleaned_links = []
    for link in pdf_links:
        # Remove trailing punctuation that might have been captured
        clean_link = re.sub(r'[.,;:)\]}"\']+$', '', link)
        cleaned_links.append(clean_link)
    
    # Remove duplicates while preserving order
    unique_links = []
    for link in cleaned_links:
        if link not in unique_links:
            unique_links.append(link)
    
    return unique_links

def finalize_answer(state: OverallState, config: RunnableConfig):
    """LangGraph node that finalizes the answer with annual report links.
    
    Args:
        state: Current graph state containing the annual report links
        config: Configuration for the runnable, including LLM provider settings
    
    Returns:
        Dictionary with state update, including messages key containing the final answer
    """
    configurable = Configuration.from_runnable_config(config)
    
    # init Gemini model
    llm = ChatGoogleGenerativeAI(
        model=configurable.answer_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = final_answer_instructions.format(
        current_date=current_date,
        holding_company=state["holding_company"],
        research_topic=get_research_topic(state["messages"]),
        summaries=state["extraction_result"] + "\n\n" + "\n---\n\n".join(state["web_research_result"]),
    )
    
    # Generate the final answer
    result = llm.invoke(formatted_prompt)
    
    # Replace the short urls with the original urls and add all used urls to the sources_gathered
    unique_sources = []
    for source in state["sources_gathered"]:
        if source["short_url"] in result.content:
            result.content = result.content.replace(
                source["short_url"], source["value"]
            )
            unique_sources.append(source)
    
    return {
        "messages": [AIMessage(content=result.content)],
        "sources_gathered": unique_sources,
    }

# Create our Agent Graph
builder = StateGraph(OverallState, config_schema=Configuration)

# Define the nodes
builder.add_node("generate_holding_company_query", generate_holding_company_query)
builder.add_node("holding_company_search", holding_company_search)
builder.add_node("generate_annual_report_query", generate_annual_report_query)
builder.add_node("annual_report_search", annual_report_search)
builder.add_node("extract_annual_reports", extract_annual_reports)
builder.add_node("finalize_answer", finalize_answer)

# Set the entrypoint
builder.add_edge(START, "generate_holding_company_query")

# Add conditional edges
builder.add_conditional_edges(
    "generate_holding_company_query", 
    continue_to_holding_company_search, 
    ["holding_company_search"]
)

# After finding the holding company, generate a query for annual reports
builder.add_edge("holding_company_search", "generate_annual_report_query")

# Add conditional edges for annual report search
builder.add_conditional_edges(
    "generate_annual_report_query", 
    continue_to_annual_report_search, 
    ["annual_report_search"]
)

# Extract annual reports from search results
builder.add_edge("annual_report_search", "extract_annual_reports")

# Finalize the answer
builder.add_edge("extract_annual_reports", "finalize_answer")
builder.add_edge("finalize_answer", END)

# Compile the graph
annual_report_graph = builder.compile(name="annual-report-search-agent")